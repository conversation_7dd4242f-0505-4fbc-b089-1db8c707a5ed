import 'package:storetrack_app/core/database/realm_database.dart';
import 'package:storetrack_app/core/utils/logger.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/features/home/<USER>/models/task_detail_model.dart';
import 'package:storetrack_app/features/home/<USER>/mappers/task_detail_mapper.dart';
import 'package:storetrack_app/core/services/photo_service.dart';
import 'package:storetrack_app/features/home/<USER>/entities/tasks_response_entity.dart';

class FormProgress {
  final int totalVisible;
  final int totalCompleted;

  FormProgress({this.totalVisible = 0, this.totalCompleted = 0});
}

/// Utility class for form-related calculations and operations
class FormUtils {
  static Future<FormProgress> getQPMDPageProgress({
    required num taskId,
    required num formId,
    required num questionId,
    num? questionpartId,
    String? questionpartMultiId,
  }) async {
    // 1. Load data from DB
    final realm = RealmDatabase.instance.realm;
    final taskModel = realm
        .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()]).firstOrNull;

    if (taskModel == null) {
      logger('Task not found for taskId: $taskId');
      return FormProgress();
    }

    final formModel = taskModel.forms
        .where((form) => form.formId == formId.toInt())
        .firstOrNull;
    if (formModel == null) {
      logger('Form not found for formId: $formId');
      return FormProgress();
    }

    final questionModel = formModel.questions
        .where((q) => q.questionId == questionId.toInt())
        .firstOrNull;
    if (questionModel == null) {
      logger('Question not found for questionId: $questionId');
      return FormProgress();
    }

    final question = TaskDetailMapper.mapSingleQuestionToEntity(questionModel);

    QuestionPart? questionPart;
    if (questionpartId != null) {
      questionPart = question.questionParts?.firstWhere(
        (qp) => qp.questionpartId == questionpartId,
        orElse: () => QuestionPart(),
      );
      if (questionpartMultiId != null && questionPart != null) {
        questionPart = QuestionPart(
          projectid: questionPart.projectid,
          questionpartId: questionPart.questionpartId,
          questionpartDescription: questionPart.questionpartDescription,
          price: questionPart.price,
          modifiedTimeStampQuestionpart:
              questionPart.modifiedTimeStampQuestionpart,
          targetByCycle: questionPart.targetByCycle,
          targetByGroup: questionPart.targetByGroup,
          targetByCompany: questionPart.targetByCompany,
          targetByRegion: questionPart.targetByRegion,
          targetByBudget: questionPart.targetByBudget,
          osaForm: questionPart.osaForm,
          companyId: questionPart.companyId,
          itemImage: questionPart.itemImage,
          targeted: questionPart.targeted,
          questionpartMultiId: questionpartMultiId,
        );
      }
    }

    // 2. Simulate state from QPMDPage
    final measurementValues = <num, dynamic>{};
    final widgetVisibility = <num, bool>{};
    final measurementPhotos = <num, List<String>>{};

    // 3. Initialize measurement values and visibility
    _initializeMeasurementState(question, measurementValues, widgetVisibility);

    // 4. Load saved answers and photos
    await _loadSavedData(taskId, formId, question, questionPart,
        measurementValues, measurementPhotos, widgetVisibility);

    // 5. Calculate progress
    final totalVisible =
        _getTotalVisibleMeasurementsCount(question, widgetVisibility);
    final totalCompleted = await _getCompletedMeasurementsCount(
      question,
      questionPart,
      widgetVisibility,
      measurementValues,
      measurementPhotos,
      taskId,
      formId,
    );

    return FormProgress(
        totalVisible: totalVisible, totalCompleted: totalCompleted);
  }

  /// Calculate progress for FQPD pages based on question parts
  static Future<FormProgress> getFQPDPageProgress({
    required num taskId,
    required num formId,
    required num questionId,
  }) async {
    // 1. Load data from DB
    final realm = RealmDatabase.instance.realm;
    final taskModel = realm
        .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()]).firstOrNull;

    if (taskModel == null) {
      logger('Task not found for taskId: $taskId');
      return FormProgress();
    }

    final formModel = taskModel.forms
        .where((form) => form.formId == formId.toInt())
        .firstOrNull;
    if (formModel == null) {
      logger('Form not found for formId: $formId');
      return FormProgress();
    }

    final questionModel = formModel.questions
        .where((q) => q.questionId == questionId.toInt())
        .firstOrNull;
    if (questionModel == null) {
      logger('Question not found for questionId: $questionId');
      return FormProgress();
    }

    final question = TaskDetailMapper.mapSingleQuestionToEntity(questionModel);

    // 2. Check if this is a restrictedMultiQuestion
    final isRestrictedMultiQuestion = question.isMulti == true &&
        question.multiMeasurementId != null &&
        question.multiMeasurementId != 0;

    // 3. Get all selected/saved question parts for this question
    final savedQuestionParts =
        await _getSavedQuestionParts(taskId, formId, questionId);

    int totalVisible;
    int totalCompleted = 0;

    if (isRestrictedMultiQuestion) {
      // For restrictedMultiQuestion, total visible is based on saved value count from other questions
      final savedValueCount = _getSavedValueCountForRestrictedMultiQuestion(
          taskId, formId, formModel, question);
      totalVisible = savedValueCount > 0 ? savedValueCount : 1;

      // Check if we have existing question parts that match the expected count
      final existingQuestionParts = _getExistingQuestionPartsForRestrictedMulti(
          taskId, formId, questionId, formModel);

      // For completion, we need to check all template slots (not just saved parts)
      final maxCount = totalVisible;
      for (int i = 0; i < maxCount; i++) {
        if (i < existingQuestionParts.length) {
          final questionPart = existingQuestionParts[i];
          if (questionPart != null &&
              questionPart.questionpartId != null &&
              questionPart.questionpartMultiId != null) {
            // Check if this question part is completed
            final qpmdProgress = await getQPMDPageProgress(
              taskId: taskId,
              formId: formId,
              questionId: questionId,
              questionpartId: questionPart.questionpartId!,
              questionpartMultiId: questionPart.questionpartMultiId!,
            );

            final isCompleted = qpmdProgress.totalVisible > 0 &&
                qpmdProgress.totalVisible == qpmdProgress.totalCompleted;

            if (isCompleted) {
              totalCompleted++;
            }
          }
        }
      }
    } else {
      // For non-restrictedMultiQuestion, count saved question parts only
      totalVisible = savedQuestionParts.length;

      if (savedQuestionParts.isEmpty) {
        return FormProgress(totalVisible: 0, totalCompleted: 0);
      }

      // Check completion for each saved question part
      for (final questionPart in savedQuestionParts) {
        if (questionPart.questionpartId == null ||
            questionPart.questionpartMultiId == null) {
          continue;
        }

        // Check if this question part is completed by using QPMD progress
        final qpmdProgress = await getQPMDPageProgress(
          taskId: taskId,
          formId: formId,
          questionId: questionId,
          questionpartId: questionPart.questionpartId!,
          questionpartMultiId: questionPart.questionpartMultiId!,
        );

        // A question part is considered completed if all its visible measurements are completed
        final isCompleted = qpmdProgress.totalVisible > 0 &&
            qpmdProgress.totalVisible == qpmdProgress.totalCompleted;

        if (isCompleted) {
          totalCompleted++;
        }
      }
    }

    return FormProgress(
      totalVisible: totalVisible,
      totalCompleted: totalCompleted,
    );
  }

  /// Calculate progress for SubHeader pages (total question parts vs completed question parts)
  /// This matches the logic used in SubHeaderCard widget by using getQPMDPageProgress for each question part
  static Future<FormProgress> getSubHeaderPageProgress({
    required num taskId,
    required num formId,
    required num questionId,
  }) async {
    // 1. Load data from DB
    final realm = RealmDatabase.instance.realm;
    final taskModel = realm
        .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()]).firstOrNull;

    if (taskModel == null) {
      logger('Task not found for taskId: $taskId');
      return FormProgress();
    }

    final formModel = taskModel.forms
        .where((form) => form.formId == formId.toInt())
        .firstOrNull;
    if (formModel == null) {
      logger('Form not found for formId: $formId');
      return FormProgress();
    }

    final questionModel = formModel.questions
        .where((q) => q.questionId == questionId.toInt())
        .firstOrNull;
    if (questionModel == null) {
      logger('Question not found for questionId: $questionId');
      return FormProgress();
    }

    final question = TaskDetailMapper.mapSingleQuestionToEntity(questionModel);

    // 2. Get all available question parts for this question (same as UI)
    final totalQuestionParts = question.questionParts?.length ?? 0;

    if (totalQuestionParts == 0) {
      return FormProgress(totalVisible: 0, totalCompleted: 0);
    }

    // 3. For each question part, check completion using the same logic as SubHeaderPage
    // by calling getQPMDPageProgress for each question part
    int totalCompleted = 0;

    for (final questionPart in question.questionParts!) {
      if (questionPart.questionpartId == null) continue;

      try {
        // Use the same method that SubHeaderPage uses to get accurate progress
        final qpmdProgress = await getQPMDPageProgress(
          taskId: taskId,
          formId: formId,
          questionId: questionId,
          questionpartId: questionPart.questionpartId!,
        );

        // A question part is considered completed if all its visible measurements are completed
        final isCompleted = qpmdProgress.totalVisible > 0 &&
            qpmdProgress.totalVisible == qpmdProgress.totalCompleted;

        if (isCompleted) {
          totalCompleted++;
        }
      } catch (e) {
        logger(
            'Error getting QPMD progress for questionPartId ${questionPart.questionpartId}: $e');
        // Don't count as completed if there's an error
      }
    }

    return FormProgress(
      totalVisible: totalQuestionParts,
      totalCompleted: totalCompleted,
    );
  }

  /// Check if a question part has any saved selections/data
  static Future<bool> _hasQuestionPartSelections(
    num taskId,
    num formId,
    num questionId,
    num questionpartId,
  ) async {
    final realm = RealmDatabase.instance.realm;
    final taskModel = realm
        .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()]).firstOrNull;

    if (taskModel == null) return false;

    final formModel = taskModel.forms
        .where((form) => form.formId == formId.toInt())
        .firstOrNull;

    if (formModel == null) return false;

    // Check if there are any saved answers for this question part
    final hasAnswers = formModel.questionAnswers
        .where((qa) =>
            qa.questionId == questionId.toInt() &&
            qa.questionpartId == questionpartId.toInt())
        .isNotEmpty;

    return hasAnswers;
  }

  /// Get all unique multi IDs for a specific question part
  static Future<List<String>> _getQuestionPartMultiIds(
    num taskId,
    num formId,
    num questionId,
    num questionpartId,
  ) async {
    final realm = RealmDatabase.instance.realm;
    final taskModel = realm
        .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()]).firstOrNull;

    if (taskModel == null) return [];

    final formModel = taskModel.forms
        .where((form) => form.formId == formId.toInt())
        .firstOrNull;

    if (formModel == null) return [];

    // Get all unique multi IDs for this question part
    final multiIds = <String>{};

    final answers = formModel.questionAnswers
        .where((qa) =>
            qa.questionId == questionId.toInt() &&
            qa.questionpartId == questionpartId.toInt())
        .toList();

    for (final answer in answers) {
      if (answer.questionPartMultiId != null) {
        multiIds.add(answer.questionPartMultiId!);
      }
    }

    return multiIds.toList();
  }

  /// Get all saved question parts for a specific question from the database
  static Future<List<QuestionPart>> _getSavedQuestionParts(
    num taskId,
    num formId,
    num questionId,
  ) async {
    final realm = RealmDatabase.instance.realm;
    final taskModel = realm
        .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()]).firstOrNull;

    if (taskModel == null) return [];

    final formModel = taskModel.forms
        .where((form) => form.formId == formId.toInt())
        .firstOrNull;

    if (formModel == null) return [];

    final questionModel = formModel.questions
        .where((q) => q.questionId == questionId.toInt())
        .firstOrNull;

    if (questionModel == null) return [];

    // Get all saved question answers for this question that have questionpartId
    // We need to find all unique questionPartMultiId values, regardless of whether
    // they have measurement data or not
    final savedAnswers = formModel.questionAnswers
        .where((qa) =>
            qa.questionId == questionId.toInt() &&
            qa.questionpartId != null &&
            qa.questionPartMultiId != null)
        .toList();

    final savedQuestionParts = <QuestionPart>[];
    final processedMultiIds = <String>{};

    // Convert saved answers back to QuestionPart entities
    for (final answer in savedAnswers) {
      if (answer.questionpartId != null && answer.questionPartMultiId != null) {
        final multiId = answer.questionPartMultiId!;

        // Skip if we've already processed this multiId
        if (processedMultiIds.contains(multiId)) {
          continue;
        }
        processedMultiIds.add(multiId);

        // Find the original question part from the question model
        final originalQuestionPart = questionModel.questionParts
            .where((qp) => qp.questionpartId == answer.questionpartId)
            .firstOrNull;

        if (originalQuestionPart != null) {
          // Create QuestionPart entity with the saved multiId
          final questionPart = QuestionPart(
            projectid: originalQuestionPart.projectid,
            questionpartId: originalQuestionPart.questionpartId,
            questionpartDescription:
                originalQuestionPart.questionpartDescription,
            price: originalQuestionPart.price,
            modifiedTimeStampQuestionpart:
                originalQuestionPart.modifiedTimeStampQuestionpart,
            targetByCycle: originalQuestionPart.targetByCycle,
            targetByGroup: originalQuestionPart.targetByGroup,
            targetByCompany: originalQuestionPart.targetByCompany,
            targetByRegion: originalQuestionPart.targetByRegion,
            targetByBudget: originalQuestionPart.targetByBudget,
            osaForm: originalQuestionPart.osaForm,
            companyId: originalQuestionPart.companyId,
            itemImage: originalQuestionPart.itemImage,
            targeted: originalQuestionPart.targeted,
            questionpartMultiId: multiId,
          );

          savedQuestionParts.add(questionPart);
        }
      }
    }

    return savedQuestionParts;
  }

  static void _initializeMeasurementState(Question question,
      Map<num, dynamic> measurementValues, Map<num, bool> widgetVisibility) {
    if (question.measurements == null) return;

    for (final measurement in question.measurements!) {
      if (measurement.measurementId != null) {
        widgetVisibility[measurement.measurementId!] =
            _getInitialVisibility(question, measurement);
        _setDefaultMeasurementValue(measurement, measurementValues);
      }
    }
  }

  static void _setDefaultMeasurementValue(
      Measurement measurement, Map<num, dynamic> measurementValues) {
    if (measurement.measurementId == null) return;
    final id = measurement.measurementId!;
    switch (measurement.measurementTypeId) {
      case 9:
        measurementValues[id] = null;
        break;
      case 1:
      case 2:
        measurementValues[id] = '';
        break;
      case 7:
        measurementValues[id] = 0;
        break;
      case 4:
      case 5:
        measurementValues[id] = null;
        break;
      case 3:
        measurementValues[id] = false;
        break;
      case 6:
        measurementValues[id] = <String>[];
        break;
      default:
        measurementValues[id] = null;
    }
  }

  static bool _getInitialVisibility(
      Question question, Measurement measurement) {
    if (measurement.measurementId == null) return true;

    for (final otherMeasurement in question.measurements!) {
      if (otherMeasurement.measurementId == measurement.measurementId) continue;

      if (otherMeasurement.measurementConditions != null) {
        for (final condition in otherMeasurement.measurementConditions!) {
          if (condition.actionMeasurementId == measurement.measurementId &&
              condition.action?.toLowerCase() == 'appear') {
            return false;
          }
        }
      }
      if (otherMeasurement.measurementConditionsMultiple != null) {
        for (final condition
            in otherMeasurement.measurementConditionsMultiple!) {
          if (condition.actionMeasurementId == measurement.measurementId &&
              condition.action?.toLowerCase() == 'appear') {
            return false;
          }
        }
      }
    }
    return true;
  }

  static Future<void> _loadSavedData(
    num taskId,
    num formId,
    Question question,
    QuestionPart? questionPart,
    Map<num, dynamic> measurementValues,
    Map<num, List<String>> measurementPhotos,
    Map<num, bool> widgetVisibility,
  ) async {
    await _loadSavedQuestionAnswers(
        taskId, formId, question, questionPart, measurementValues);
    await _loadSavedPhotos(taskId, question, questionPart, measurementPhotos);
    _processInitialConditionalLogic(question, measurementValues,
        widgetVisibility); // Must be after loading answers
  }

  static Future<void> _loadSavedQuestionAnswers(
    num taskId,
    num formId,
    Question question,
    QuestionPart? questionPart,
    Map<num, dynamic> measurementValues,
  ) async {
    final realm = RealmDatabase.instance.realm;
    final taskModel = realm
        .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()]).firstOrNull;
    if (taskModel == null) return;

    final formModel = taskModel.forms
        .where((form) => form.formId == formId.toInt())
        .firstOrNull;
    if (formModel == null) return;

    final questionAnswers = formModel.questionAnswers
        .where((qa) =>
            qa.questionId == question.questionId!.toInt() &&
            qa.questionpartId == questionPart?.questionpartId?.toInt())
        .where((qa) {
      final multiId = questionPart?.questionpartMultiId;
      if (multiId != null && multiId.contains('-')) {
        return qa.questionPartMultiId == multiId;
      } else {
        return qa.questionPartMultiId == null ||
            qa.questionPartMultiId == questionPart?.questionpartId?.toString();
      }
    }).toList();

    for (final qa in questionAnswers) {
      if (qa.measurementId != null) {
        final measurement = _findMeasurementById(question, qa.measurementId!);
        if (measurement == null) continue;

        dynamic restoredValue =
            _restoreValueFromQuestionAnswer(qa, measurement);
        if (restoredValue != null) {
          measurementValues[qa.measurementId!] = restoredValue;
        }
      }
    }
  }

  static Future<void> _loadSavedPhotos(
    num taskId,
    Question question,
    QuestionPart? questionPart,
    Map<num, List<String>> measurementPhotos,
  ) async {
    final photoService = sl<PhotoService>();
    final savedPhotos =
        await photoService.getPhotosFromTask(taskId: taskId.toInt());

    final filteredPhotos = savedPhotos.where((photo) {
      return photo.questionId == question.questionId?.toInt() &&
          photo.questionpartId == questionPart?.questionpartId?.toInt() &&
          photo.combineTypeId == 3;
    }).toList();

    measurementPhotos.clear();
    for (final photo in filteredPhotos) {
      if (photo.measurementId != null && photo.photoUrl != null) {
        final measurementId = photo.measurementId!;
        if (!measurementPhotos.containsKey(measurementId)) {
          measurementPhotos[measurementId] = [];
        }
        measurementPhotos[measurementId]!.add(photo.photoUrl!);
      }
    }
  }

  static void _processInitialConditionalLogic(Question question,
      Map<num, dynamic> measurementValues, Map<num, bool> widgetVisibility) {
    if (question.measurements == null) return;
    for (final measurement in question.measurements!) {
      if (measurement.measurementId == null) continue;
      if (_canTriggerConditionalLogic(measurement)) {
        final value = measurementValues[measurement.measurementId!];
        if (value != null &&
            !_isValueEmpty(value, measurement.measurementTypeId)) {
          _processConditionalLogic(
              question, measurement, value, widgetVisibility,
              isInitialization: true);
        }
      }
    }
  }

  static dynamic _restoreValueFromQuestionAnswer(
      QuestionAnswerModel qa, Measurement measurement) {
    switch (measurement.measurementTypeId) {
      case 1:
      case 2:
        return qa.measurementTextResult ?? '';
      case 3:
        return qa.measurementOptionId != null && qa.measurementOptionId! > 0;
      case 4:
      case 5:
        if (qa.measurementOptionId != null &&
            measurement.measurementOptions != null) {
          final option = measurement.measurementOptions!
              .where((opt) => opt.measurementOptionId == qa.measurementOptionId)
              .firstOrNull;
          return option?.measurementOptionDescription;
        }
        return null;
      case 6:
        if (qa.measurementOptionIds != null &&
            qa.measurementOptionIds!.isNotEmpty) {
          return qa.measurementOptionIds!
              .split(',')
              .map((id) => id.trim())
              .where((id) => id.isNotEmpty)
              .toList();
        }
        return <String>[];
      case 7:
        if (qa.measurementTextResult != null) {
          return int.tryParse(qa.measurementTextResult!) ?? 0;
        }
        return 0;
      case 9:
        if (qa.measurementTextResult != null) {
          return qa.measurementTextResult;
        }
        return null;
      default:
        return qa.measurementTextResult;
    }
  }

  static int _getTotalVisibleMeasurementsCount(
      Question question, Map<num, bool> widgetVisibility) {
    if (question.measurements == null) return 0;
    int totalCount = 0;
    for (final measurement in question.measurements!) {
      if (measurement.measurementId != null) {
        if (widgetVisibility[measurement.measurementId!] ?? true) {
          totalCount++;
        }
      }
    }
    return totalCount;
  }

  static Future<int> _getCompletedMeasurementsCount(
    Question question,
    QuestionPart? questionPart,
    Map<num, bool> widgetVisibility,
    Map<num, dynamic> measurementValues,
    Map<num, List<String>> measurementPhotos,
    num taskId,
    num formId,
  ) async {
    if (question.measurements == null) return 0;
    int completedCount = 0;

    for (final measurement in question.measurements!) {
      if (measurement.measurementId == null) continue;
      final id = measurement.measurementId!;

      if (!(widgetVisibility[id] ?? true)) continue;

      final value = measurementValues[id];

      if (!_isValueEmpty(value, measurement.measurementTypeId)) {
        final error = _validateMeasurement(measurement, value);
        final photoError = _validatePhotos(
            question, questionPart, measurement, measurementPhotos);
        final quizError =
            await _validateQuizAnswers(taskId, formId, measurement, value);

        if (error == null && photoError == null && quizError == null) {
          completedCount++;
        }
      }
    }
    return completedCount;
  }

  static bool _isValueEmpty(dynamic value, num? measurementTypeId) {
    switch (measurementTypeId) {
      case 1:
      case 2:
        return value == null || value.toString().trim().isEmpty;
      case 3:
        return value == null || value == false;
      case 4:
      case 5:
      case 9:
        return value == null;
      case 6:
        return value == null || (value is List && value.isEmpty);
      case 7:
        return value == null || value == 0;
      default:
        return value == null;
    }
  }

  static String? _validateMeasurement(Measurement measurement, dynamic value) {
    if (measurement.measurementValidations == null) return null;
    for (final validation in measurement.measurementValidations!) {
      if (validation.validationTypeId == 1 && validation.required == true) {
        if (_isValueEmpty(value, measurement.measurementTypeId)) {
          return validation.errorMessage ?? 'This field is required';
        }
      }
    }
    return null;
  }

  static String? _validatePhotos(
    Question question,
    QuestionPart? questionPart,
    Measurement measurement,
    Map<num, List<String>> measurementPhotos,
  ) {
    final cameraInfo = _getCameraIconInfo(question, questionPart, measurement);
    if (!cameraInfo['show']) return null;

    final photoTag =
        _getPhotoTagForMeasurement(question, questionPart, measurement);
    if (photoTag == null) return null;

    final requiredPhotos = photoTag.numberOfPhotos?.toInt() ?? 0;
    if (requiredPhotos <= 0) return null;

    final uploadedPhotos =
        measurementPhotos[measurement.measurementId!]?.length ?? 0;
    if (uploadedPhotos < requiredPhotos) {
      return 'Please upload at least $requiredPhotos photo(s).';
    }
    return null;
  }

  static Future<String?> _validateQuizAnswers(
    num taskId,
    num formId,
    Measurement measurement,
    dynamic value,
  ) async {
    if (!await _isQuizForm(taskId, formId)) return null;
    if (![4, 5, 6].contains(measurement.measurementTypeId)) return null;
    if (_isValueEmpty(value, measurement.measurementTypeId)) return null;

    if ([4, 5].contains(measurement.measurementTypeId)) {
      return _validateDropdownQuizAnswer(measurement, value);
    } else if (measurement.measurementTypeId == 6) {
      return _validateMultiSelectQuizAnswer(measurement, value);
    }
    return null;
  }

  static Future<bool> _isQuizForm(num taskId, num formId) async {
    final realm = RealmDatabase.instance.realm;
    final taskModel = realm
        .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()]).firstOrNull;
    if (taskModel == null) return false;
    final formModel = taskModel.forms
        .where((form) => form.formId == formId.toInt())
        .firstOrNull;
    return formModel?.formTypeId == 12;
  }

  static String? _validateDropdownQuizAnswer(
      Measurement measurement, dynamic value) {
    if (measurement.measurementOptions == null) return null;
    try {
      final selectedOption = measurement.measurementOptions!
          .firstWhere((opt) => opt.measurementOptionDescription == value);
      if (selectedOption.isAnswer != true) {
        return 'The answer you selected is incorrect';
      }
    } catch (e) {
      return 'The answer you selected is incorrect';
    }
    return null;
  }

  static String? _validateMultiSelectQuizAnswer(
      Measurement measurement, dynamic value) {
    if (measurement.measurementOptions == null) return null;
    if (value is! List) return null;

    final correctOptions =
        measurement.measurementOptions!.where((opt) => opt.isAnswer == true);
    final incorrectOptions =
        measurement.measurementOptions!.where((opt) => opt.isAnswer == false);

    final selectedOptionIds = <int>{};
    for (final selectedValue in value) {
      final optionId = int.tryParse(selectedValue.toString());
      if (optionId != null) {
        selectedOptionIds.add(optionId);
      }
    }

    for (final correctOption in correctOptions) {
      if (!selectedOptionIds
          .contains(correctOption.measurementOptionId?.toInt())) {
        return 'Some of the answers are incorrect';
      }
    }

    for (final incorrectOption in incorrectOptions) {
      if (selectedOptionIds
          .contains(incorrectOption.measurementOptionId?.toInt())) {
        return 'Some of the answers are incorrect';
      }
    }

    return null;
  }

  static Map<String, dynamic> _getCameraIconInfo(
      Question question, QuestionPart? questionPart, Measurement measurement) {
    final result = {'show': false, 'isMandatory': false};
    if (question.photoTagsThree == null || measurement.measurementId == null) {
      return result;
    }
    for (final photoTag in question.photoTagsThree!) {
      if (photoTag.measurementId == measurement.measurementId &&
          photoTag.questionpartId == questionPart?.questionpartId) {
        result['show'] = true;
        result['isMandatory'] = photoTag.isMandatory == true;
        break;
      }
    }
    return result;
  }

  static PhotoTagsT? _getPhotoTagForMeasurement(
      Question question, QuestionPart? questionPart, Measurement measurement) {
    if (question.photoTagsThree == null || measurement.measurementId == null) {
      return null;
    }
    return question.photoTagsThree!.firstWhere(
        (pt) =>
            pt.measurementId == measurement.measurementId &&
            pt.questionpartId == questionPart?.questionpartId,
        orElse: () => PhotoTagsT());
  }

  static Measurement? _findMeasurementById(
      Question question, num measurementId) {
    try {
      return question.measurements
          ?.firstWhere((m) => m.measurementId == measurementId);
    } catch (e) {
      return null;
    }
  }

  static bool _canTriggerConditionalLogic(Measurement measurement) {
    final hasConditions =
        (measurement.measurementConditions?.isNotEmpty ?? false) ||
            (measurement.measurementConditionsMultiple?.isNotEmpty ?? false);
    final canTrigger = [3, 4, 5, 6, 8].contains(measurement.measurementTypeId);
    return hasConditions && canTrigger;
  }

  static void _processConditionalLogic(
      Question question,
      Measurement measurement,
      dynamic selectedValue,
      Map<num, bool> widgetVisibility,
      {bool isInitialization = false}) {
    if (question.isMll == null || selectedValue == null) return;

    final selectedOptionId =
        _getSelectedOptionIdForConditionalLogic(measurement, selectedValue);
    if (selectedOptionId == null) return;

    final conditions = question.isMll == false
        ? measurement.measurementConditions
        : measurement.measurementConditionsMultiple;

    if (conditions == null) return;

    for (final condition in conditions) {
      if (condition.measurementId == measurement.measurementId &&
          condition.measurementOptionId == selectedOptionId) {
        if (condition.actionMeasurementId != null && condition.action != null) {
          _applyConditionalAction(condition.actionMeasurementId!,
              condition.action!, widgetVisibility,
              isInitialization: isInitialization);
        }
      }
    }
  }

  static void _applyConditionalAction(
      num targetMeasurementId, String action, Map<num, bool> widgetVisibility,
      {bool isInitialization = false}) {
    if (action.toLowerCase() == 'appear') {
      widgetVisibility[targetMeasurementId] = true;
    } else if (action.toLowerCase() == 'disappear') {
      widgetVisibility[targetMeasurementId] = false;
      // In a real scenario, we would also clear values and cascade,
      // but for progress calculation, just hiding is enough.
    }
  }

  static num? _getSelectedOptionIdForConditionalLogic(
      Measurement measurement, dynamic selectedValue) {
    switch (measurement.measurementTypeId) {
      case 3:
        return selectedValue == true ? 1 : 2;
      case 4:
      case 5:
      case 8:
        return _getSelectedOptionId(measurement, selectedValue);
      case 6:
        return null;
      default:
        return null;
    }
  }

  static num? _getSelectedOptionId(
      Measurement measurement, dynamic selectedValue) {
    if (measurement.measurementOptions == null) return null;
    try {
      final selectedOption = measurement.measurementOptions!
          .firstWhere((o) => o.measurementOptionDescription == selectedValue);
      return selectedOption.measurementOptionId;
    } catch (e) {
      return null;
    }
  }

  /// Get progress for the next page from a question page based on navigation logic
  static Future<FormProgress> getQuestionPageProgress({
    required num taskId,
    required num formId,
    required Question question,
  }) async {
    final questionParts = question.questionParts ?? [];
    final hasSignature = question.hasSignature ?? false;
    final isMulti = question.isMulti ?? false;

    if (questionParts.length != 1 || hasSignature) {
      if (isMulti) {
        // Navigate to FQPDPage for multi questions
        return await getFQPDPageProgress(
          taskId: taskId,
          formId: formId,
          questionId: question.questionId!,
        );
      } else {
        // Navigate to SubHeaderPage for questions with multiple parts or signature
        var a = await getSubHeaderPageProgress(
          taskId: taskId,
          formId: formId,
          questionId: question.questionId!,
        );
        logger(
            'getSubHeaderPageProgress: ${a.totalVisible} ${a.totalCompleted}');
        return a;
      }
    } else {
      // Navigate to QPMDPage for single measurement questions
      return await getQPMDPageProgress(
        taskId: taskId,
        formId: formId,
        questionId: question.questionId!,
        questionpartId: questionParts.first.questionpartId!,
      );
    }
  }

  /// Get the count of items displayed in QuestionPage for given taskId and formId
  /// This replicates the visibility logic from QuestionPage to count visible questions
  static Future<FormProgress> getQuestionPageItemsCount({
    required num formId,
    required num? taskId,
  }) async {
    if (taskId == null) return FormProgress();

    try {
      // Load form from database using formId
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [taskId.toInt()]).firstOrNull;

      if (taskModel == null) {
        logger('Task not found for taskId: $taskId');
        return FormProgress();
      }

      // Find the form with the matching formId
      final formModel = taskModel.forms
          .where((form) => form.formId == formId.toInt())
          .firstOrNull;

      if (formModel == null) {
        logger('Form not found for formId: $formId');
        return FormProgress();
      }

      // Convert FormModel to Form entity
      final form = TaskDetailMapper.toEntity(taskModel);
      final formEntity =
          form.forms?.where((f) => f.formId == formId).firstOrNull;

      if (formEntity == null) {
        logger('Form entity not found');
        return FormProgress();
      }

      // Initialize question visibility (same logic as QuestionPage._initializeQuestionVisibility)
      final questionVisibility = <num, bool>{};
      if (formEntity.questions != null) {
        for (final question in formEntity.questions!) {
          if (question.questionId != null) {
            // All questions are visible by default
            questionVisibility[question.questionId!] = true;
          }
        }
      }

      // Process question conditional logic (same as QuestionPage._processQuestionConditionalLogic)
      await _processQuestionConditionalLogicForCount(
          taskId, formEntity, formModel, questionVisibility);

      // Handle restricted multi-parent question visibility logic (same as QuestionPage._handleRestrictedMultiQuestionVisibility)
      _handleRestrictedMultiQuestionVisibilityForCount(
          taskId, formEntity, formModel, questionVisibility);

      // Count visible questions and calculate completed questions
      int visibleCount = 0;
      int completedCount = 0;

      if (formEntity.questions != null) {
        for (final question in formEntity.questions!) {
          // Always show questions without questionId (like comment items)
          if (question.questionId == null) {
            visibleCount++;
            // Comment questions without IDs are always considered completed
            completedCount++;
          } else {
            // Show questions based on visibility state (default to true if not set)
            final isVisible = questionVisibility[question.questionId!] ?? true;
            if (isVisible) {
              visibleCount++;

              // Check if this question is completed
              try {
                final questionProgress = await getQuestionPageProgress(
                  taskId: taskId,
                  formId: formId,
                  question: question,
                );

                // A question is considered completed if all its items are completed
                if (questionProgress.totalVisible > 0 &&
                    questionProgress.totalVisible ==
                        questionProgress.totalCompleted) {
                  completedCount++;
                }
              } catch (e) {
                logger(
                    'Error calculating progress for question ${question.questionId}: $e');
                // Don't count as completed if there's an error
              }
            }
          }
        }
      }

      return FormProgress(
          totalVisible: visibleCount, totalCompleted: completedCount);
    } catch (e) {
      logger('Error getting question page items count: $e');
      return FormProgress();
    }
  }

  /// Process question conditional logic for counting purposes
  static Future<void> _processQuestionConditionalLogicForCount(
    num taskId,
    Form formEntity,
    FormModel formModel,
    Map<num, bool> questionVisibility,
  ) async {
    if (formEntity.questions == null) return;

    try {
      // Get all saved QuestionAnswer objects for this form
      final savedAnswers = formModel.questionAnswers;

      // Process each question's conditions
      for (final question in formEntity.questions!) {
        if (question.questionConditions == null ||
            question.questionConditions!.isEmpty) {
          continue;
        }

        // Process each condition for this question
        for (final condition in question.questionConditions!) {
          if (condition.measurementId == null ||
              condition.measurementOptionId == null ||
              condition.actionQuestionId == null ||
              condition.action == null) {
            continue;
          }

          // Find matching saved answer for this measurement
          final matchingAnswer = savedAnswers
              .where((answer) =>
                  answer.measurementId == condition.measurementId!.toInt() &&
                  answer.measurementOptionId ==
                      condition.measurementOptionId!.toInt())
              .firstOrNull;

          if (matchingAnswer != null) {
            // Apply the conditional action
            _applyQuestionConditionalActionForCount(
              condition.actionQuestionId!,
              condition.action!,
              questionVisibility,
            );
          }
        }
      }
    } catch (e) {
      logger('Error processing question conditional logic for count: $e');
    }
  }

  /// Apply conditional action for counting purposes
  static void _applyQuestionConditionalActionForCount(
    num targetQuestionId,
    String action,
    Map<num, bool> questionVisibility,
  ) {
    if (action.toLowerCase() == 'appear') {
      questionVisibility[targetQuestionId] = true;
    } else if (action.toLowerCase() == 'disappear') {
      questionVisibility[targetQuestionId] = false;
    }
  }

  /// Handle restricted multi question visibility logic for counting purposes
  static void _handleRestrictedMultiQuestionVisibilityForCount(
    num taskId,
    Form formEntity,
    FormModel formModel,
    Map<num, bool> questionVisibility,
  ) {
    if (formEntity.questions == null || formEntity.questions!.isEmpty) return;

    try {
      // Step 1: Identify restrictedMultiQuestion
      Question? restrictedMultiQuestion;

      for (final question in formEntity.questions!) {
        if (question.isMulti == true &&
            question.multiMeasurementId != null &&
            question.multiMeasurementId != 0) {
          restrictedMultiQuestion = question;
          break;
        }
      }

      // Step 2: If no restrictedMultiQuestion found, return early
      if (restrictedMultiQuestion == null) {
        return;
      }

      // Step 3: Check if any OTHER question (not the restrictedMultiQuestion) has saved counter value > 0
      final hasOtherQuestionWithValue =
          _hasOtherQuestionWithCounterValueForCount(
              restrictedMultiQuestion, formEntity, formModel);

      // Step 4: Apply visibility rules
      if (hasOtherQuestionWithValue) {
        // Make ALL questions visible (including restrictedMultiQuestion) when other questions have value > 0
        for (final question in formEntity.questions!) {
          if (question.questionId != null) {
            questionVisibility[question.questionId!] = true;
          }
        }
      } else {
        // Initially: Hide the restrictedMultiQuestion, show all other questions
        for (final question in formEntity.questions!) {
          if (question.questionId != null) {
            if (question.questionId == restrictedMultiQuestion.questionId) {
              // Hide the restrictedMultiQuestion
              questionVisibility[question.questionId!] = false;
            } else {
              // Show all other questions
              questionVisibility[question.questionId!] = true;
            }
          }
        }
      }
    } catch (e) {
      logger('Error in _handleRestrictedMultiQuestionVisibilityForCount: $e');
    }
  }

  /// Check if any OTHER question has saved counter value > 0 for counting purposes
  static bool _hasOtherQuestionWithCounterValueForCount(
    Question restrictedMultiQuestion,
    Form formEntity,
    FormModel formModel,
  ) {
    if (formEntity.questions == null) return false;

    try {
      // Check all questions except the restrictedMultiQuestion
      for (final question in formEntity.questions!) {
        if (question.questionId == null ||
            question.questionId == restrictedMultiQuestion.questionId) {
          continue; // Skip the restrictedMultiQuestion itself
        }

        // Look for QuestionAnswer entries for this question with counter values
        final questionAnswers = formModel.questionAnswers
            .where(
                (answer) => answer.questionId == question.questionId!.toInt())
            .toList();

        for (final answer in questionAnswers) {
          if (answer.measurementTextResult != null) {
            final counterValue = int.tryParse(answer.measurementTextResult!);
            if (counterValue != null && counterValue > 0) {
              return true;
            }
          }
        }
      }

      return false;
    } catch (e) {
      logger('Error checking other questions counter values for count: $e');
      return false;
    }
  }

  /// Get saved value count for restrictedMultiQuestion by looking at counter values from other questions
  static int _getSavedValueCountForRestrictedMultiQuestion(
    num taskId,
    num formId,
    FormModel formModel,
    Question question,
  ) {
    try {
      int maxCount = 0;

      // Get all question answers for this form
      final allQuestionAnswers = formModel.questionAnswers.toList();

      // Group by questionId to find counter values from other questions
      final questionGroups = <int, List<QuestionAnswerModel>>{};
      for (final answer in allQuestionAnswers) {
        if (answer.questionId != null) {
          questionGroups.putIfAbsent(answer.questionId!, () => []).add(answer);
        }
      }

      // Check each question group (excluding the restrictedMultiQuestion itself)
      for (final entry in questionGroups.entries) {
        final questionId = entry.key;
        final answers = entry.value;

        // Skip the restrictedMultiQuestion itself
        if (questionId == question.questionId!.toInt()) {
          continue;
        }

        // Look for counter values in this question's answers
        for (final answer in answers) {
          if (answer.measurementTextResult != null) {
            final counterValue = int.tryParse(answer.measurementTextResult!);
            if (counterValue != null && counterValue > maxCount) {
              maxCount = counterValue;
            }
          }
        }
      }

      return maxCount;
    } catch (e) {
      logger('Error getting saved value count for restrictedMultiQuestion: $e');
      return 0;
    }
  }

  /// Get existing question parts for restrictedMultiQuestion that have proper questionpartMultiId format
  static List<QuestionPart?> _getExistingQuestionPartsForRestrictedMulti(
    num taskId,
    num formId,
    num questionId,
    FormModel formModel,
  ) {
    try {
      // Find saved question answers for this question
      final savedAnswers = formModel.questionAnswers
          .where((answer) => answer.questionId == questionId.toInt())
          .toList();

      if (savedAnswers.isEmpty) {
        return [];
      }

      // Check for existing question parts with proper questionpartMultiId format
      final existingQuestionParts = <QuestionPart?>[];
      final processedMultiIds = <String>{};

      for (final answer in savedAnswers) {
        if (answer.questionpartId != null &&
            answer.questionPartMultiId != null) {
          final multiId = answer.questionPartMultiId!;

          // Skip if we've already processed this multiId
          if (processedMultiIds.contains(multiId)) {
            continue;
          }

          // Check if the multiId has the format "a-b" where "a" equals questionpartId
          final parts = multiId.split('-');
          if (parts.length >= 2) {
            final aPart = parts[0];
            final questionPartIdStr = answer.questionpartId.toString();

            if (aPart == questionPartIdStr) {
              // This is a valid existing question part
              processedMultiIds.add(multiId);

              // Find the question part from the realm model
              final realm = RealmDatabase.instance.realm;
              final taskModel = realm.query<TaskDetailModel>(
                  'taskId == \$0', [taskId.toInt()]).firstOrNull;

              if (taskModel != null) {
                final formModel = taskModel.forms
                    .where((form) => form.formId == formId.toInt())
                    .firstOrNull;

                if (formModel != null) {
                  final questionModel = formModel.questions
                      .where((q) => q.questionId == questionId.toInt())
                      .firstOrNull;

                  if (questionModel != null) {
                    final originalQuestionPart = questionModel.questionParts
                        .where(
                            (qp) => qp.questionpartId == answer.questionpartId)
                        .firstOrNull;

                    if (originalQuestionPart != null) {
                      final questionPart = QuestionPart(
                        projectid: originalQuestionPart.projectid,
                        questionpartId: originalQuestionPart.questionpartId,
                        questionpartDescription:
                            originalQuestionPart.questionpartDescription,
                        price: originalQuestionPart.price,
                        modifiedTimeStampQuestionpart:
                            originalQuestionPart.modifiedTimeStampQuestionpart,
                        targetByCycle: originalQuestionPart.targetByCycle,
                        targetByGroup: originalQuestionPart.targetByGroup,
                        targetByCompany: originalQuestionPart.targetByCompany,
                        targetByRegion: originalQuestionPart.targetByRegion,
                        targetByBudget: originalQuestionPart.targetByBudget,
                        osaForm: originalQuestionPart.osaForm,
                        companyId: originalQuestionPart.companyId,
                        itemImage: originalQuestionPart.itemImage,
                        targeted: originalQuestionPart.targeted,
                        questionpartMultiId: multiId,
                      );

                      existingQuestionParts.add(questionPart);
                    }
                  }
                }
              }
            }
          }
        }
      }

      return existingQuestionParts;
    } catch (e) {
      logger(
          'Error getting existing question parts for restrictedMultiQuestion: $e');
      return [];
    }
  }

  /// Get the progress for FormPage by counting total forms and completed forms for a given taskId
  /// Returns QPMDProgress with totalVisible as number of forms and totalCompleted as number of completed forms
  static Future<FormProgress> getFormPageProgress({
    required num taskId,
  }) async {
    try {
      // Load task from database using taskId
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel = realm.query<TaskDetailModel>(
          'taskId == \$0', [taskId.toInt()]).firstOrNull;

      if (taskModel == null) {
        logger('Task not found for taskId: $taskId');
        return FormProgress();
      }

      // Convert TaskDetailModel to TaskDetail entity
      final taskEntity = TaskDetailMapper.toEntity(taskModel);
      final forms = taskEntity.forms ?? [];

      if (forms.isEmpty) {
        return FormProgress(totalVisible: 0, totalCompleted: 0);
      }

      int totalForms = 0;
      int completedForms = 0;

      // Check each form to see if it's completed
      for (final form in forms) {
        if (form.formId == null) continue;

        // Check if this form is completed by using kTotal and kCompleted from database
        final formModel = taskModel.forms
            .where((f) => f.formId == form.formId!.toInt())
            .firstOrNull;

        if (formModel != null && formModel.isVisionForm != true) {
          totalForms++;
          final kTotal = formModel.kTotal;
          final kCompleted = formModel.kCompleted;

          bool isFormCompleted = false;

          if (kTotal != null && kCompleted != null) {
            // Use database values if available
            isFormCompleted = kTotal > 0 && kCompleted >= kTotal;
          } else {
            // Fallback: Calculate completion by checking each question's progress
            final formEntity = taskEntity.forms
                ?.where((f) => f.formId == form.formId!)
                .firstOrNull;

            if (formEntity != null && formEntity.questions != null) {
              int totalQuestions = 0;
              int completedQuestions = 0;

              for (final question in formEntity.questions!) {
                if (question.questionId == null) {
                  // Comment questions without IDs are always considered completed
                  totalQuestions++;
                  completedQuestions++;
                  continue;
                }

                totalQuestions++;

                // Get progress for this specific question using existing methods
                final questionProgress = await getQuestionPageProgress(
                  taskId: taskId,
                  formId: form.formId!,
                  question: question,
                );

                // A question is completed if all its items are completed
                if (questionProgress.totalVisible > 0 &&
                    questionProgress.totalVisible ==
                        questionProgress.totalCompleted) {
                  completedQuestions++;
                }
              }

              // Form is completed if all questions are completed
              isFormCompleted =
                  totalQuestions > 0 && completedQuestions >= totalQuestions;
            }
          }

          if (isFormCompleted) {
            completedForms++;
          }
        }
      }

      return FormProgress(
        totalVisible: totalForms,
        totalCompleted: completedForms,
      );
    } catch (e) {
      logger('Error getting form page progress: $e');
      return FormProgress();
    }
  }

  static bool isFormCompleted(Form form) {
    final formModel = form;
    final kTotal = formModel.kTotal ?? 0;
    final kCompleted = formModel.kCompleted ?? 0;
    return kTotal > 0 && kCompleted >= kTotal;
  }

  static BudgetCalculationResult? calculateBudget(int taskId) {
    final realm = RealmDatabase.instance.realm;
    final taskModel = realm
        .query<TaskDetailModel>('taskId == \$0', [taskId.toInt()]).firstOrNull;

    if (taskModel == null) {
      logger('Task not found for taskId: $taskId');
      return BudgetCalculationResult(
        originalBudget: 0,
        tempBudget: 0,
        budgetHasChanged: false,
      );
    }

    var taskEntity = TaskDetailMapper.toEntity(taskModel);

    var forms = taskEntity.forms;
    var originalBudget = taskEntity.budget?.toInt() ?? 0;
    var tempBudget = taskEntity.budget?.toInt() ?? 0;

    var totalBudgetOffsetStructure = <BudgetOffset>[];
    var totalBudgetOffsetStructureType1 = <BudgetOffset>[];
    var totalBudgetOffsetStructureType2 = <BudgetOffset>[];

    for (var form in forms!) {
      logger("budget form completed: ${isFormCompleted(form)}");
      if (true) {
        if (form.questions != null && form.questions!.isNotEmpty) {
          for (var question in form.questions!) {
            if (question.isComment == false) {
              for (var measurement in question.measurements!) {
                for (var option in measurement.measurementOptions!) {
                  if (option.budgetOffset != null &&
                      (int.tryParse(option.budgetOffset.toString()) ?? 0) !=
                          0 &&
                      option.budgetOffsetType != null &&
                      (int.tryParse(option.budgetOffsetType.toString()) ?? 0) ==
                          1) {
                    logger('budget offset type 1: ${option.budgetOffset}');
                    var budgetOffset = BudgetOffset(
                      formId: form.formId!.toInt(),
                      questionId: question.questionId!.toInt(),
                      measurementId: measurement.measurementId!.toInt(),
                      measurementOptionId: option.measurementOptionId!.toInt(),
                      budgetOffset: option.budgetOffset!.toInt(),
                      budgetOffsetType: option.budgetOffsetType!.toInt(),
                    );
                    totalBudgetOffsetStructure.add(budgetOffset);
                  }
                  if (option.budgetOffset != null &&
                      (int.tryParse(option.budgetOffset.toString()) ?? 0) !=
                          0 &&
                      option.budgetOffsetType != null &&
                      (int.tryParse(option.budgetOffsetType.toString()) ?? 0) ==
                          2) {
                    logger('budget offset type 2: ${option.budgetOffset}');
                    var budgetOffset = BudgetOffset(
                      formId: form.formId!.toInt(),
                      questionId: question.questionId!.toInt(),
                      measurementId: measurement.measurementId!.toInt(),
                      measurementOptionId: option.measurementOptionId!.toInt(),
                      budgetOffset: option.budgetOffset!.toInt(),
                      budgetOffsetType: option.budgetOffsetType!.toInt(),
                    );
                    totalBudgetOffsetStructureType2.add(budgetOffset);
                  }
                }
              }
            }
          }
        }
      }
    }

    totalBudgetOffsetStructureType1 = totalBudgetOffsetStructure;
    totalBudgetOffsetStructure.addAll(totalBudgetOffsetStructureType2);

    for (var form in forms) {
      if (true) {
        if (form.questionAnswers != null && form.questionAnswers!.isNotEmpty) {
          for (var answer in form.questionAnswers!) {
            var offset = 0;
            for (var budgetOffset in totalBudgetOffsetStructure) {
              if (budgetOffset.formId == answer.formId &&
                  budgetOffset.questionId == answer.questionId &&
                  budgetOffset.measurementId == answer.measurementId &&
                  budgetOffset.measurementOptionId ==
                      answer.measurementOptionId &&
                  budgetOffset.budgetOffsetType == 2) {
                logger('budget offset type 2: ${budgetOffset.budgetOffset}');
                offset = budgetOffset.budgetOffset;
                tempBudget = 0;
              } else {
                offset = shouldApplyBudgetOffset(
                    answer, totalBudgetOffsetStructureType1);
              }
            }
            if (offset != 0) {
              tempBudget = tempBudget + offset;
              logger('TMPBUDGET: $tempBudget');
            }
          }
        }
      }
    }

    if (tempBudget < 0) {
      tempBudget = 0;
    }

    if (tempBudget != originalBudget) {
      logger('Budget has changed: $originalBudget -> $tempBudget');
    } else {
      logger('Budget has not changed: $originalBudget -> $tempBudget');
    }

    return BudgetCalculationResult(
      originalBudget: originalBudget,
      tempBudget: tempBudget,
      budgetHasChanged: tempBudget != originalBudget,
    );
  }

  static int shouldApplyBudgetOffset(
    QuestionAnswer answer,
    List<BudgetOffset> totalBudgetOffsetStructure,
  ) {
    if (answer.measurementTypeId != null) {
      if (answer.measurementTypeId == 6) {
        var ids = answer.measurementOptionIds;
        if (ids != null && ids.isNotEmpty) {
          var idList = ids.split(',');
          var totalOffset = 0;
          for (var id in idList) {
            for (var budgetOffset in totalBudgetOffsetStructure) {
              if (budgetOffset.formId == answer.formId &&
                  budgetOffset.questionId == answer.questionId &&
                  budgetOffset.measurementId == answer.measurementId &&
                  budgetOffset.measurementOptionId.toString() == id) {
                totalOffset = totalOffset +
                    (int.tryParse(budgetOffset.budgetOffset.toString()) ?? 0);
              }
            }
          }
          return totalOffset;
        }
      } else {
        for (var budgetOffset in totalBudgetOffsetStructure) {
          if (budgetOffset.formId == answer.formId &&
              budgetOffset.questionId == answer.questionId &&
              budgetOffset.measurementId == answer.measurementId &&
              budgetOffset.measurementOptionId == answer.measurementOptionId) {
            return (int.tryParse(budgetOffset.budgetOffset.toString()) ?? 0);
          }
        }
      }
    }
    return 0;
  }

  /// Get task entity by ID from the database
  static Future<TaskDetail?> getTaskById(int taskId) async {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

      if (taskModel == null) {
        logger('Task not found for taskId: $taskId');
        return null;
      }

      // Convert TaskDetailModel to TaskDetail entity
      final taskDetail = TaskDetailMapper.toEntity(taskModel);

      return taskDetail;
    } catch (e) {
      logger('Error getting task by ID $taskId: $e');
      return null;
    }
  }

  /// Get task entity and specific form model by IDs from the database
  /// Returns a map with 'task' (TaskDetail entity) and 'formModel' (FormModel) keys
  static Future<Map<String, dynamic>?> getTaskWithFormModel(
      int taskId, int formId) async {
    try {
      final realm = RealmDatabase.instance.realm;

      // Find the task with the matching taskId
      final taskModel =
          realm.query<TaskDetailModel>('taskId == \$0', [taskId]).firstOrNull;

      if (taskModel == null) {
        logger('Task not found for taskId: $taskId');
        return null;
      }

      // Find the form with the matching formId
      final formModel =
          taskModel.forms.where((f) => f.formId == formId).firstOrNull;

      if (formModel == null) {
        logger('Form not found for formId: $formId in taskId: $taskId');
        return null;
      }

      // Convert TaskDetailModel to TaskDetail entity
      final taskDetail = TaskDetailMapper.toEntity(taskModel);

      return {
        'task': taskDetail,
        'formModel': formModel,
      };
    } catch (e) {
      logger(
          'Error getting task with form model for taskId: $taskId, formId: $formId - $e');
      return null;
    }
  }
}

class BudgetOffset {
  final int formId;
  final int questionId;
  final int measurementId;
  final int measurementOptionId;
  final int budgetOffset;
  final int budgetOffsetType;

  BudgetOffset({
    required this.formId,
    required this.questionId,
    required this.measurementId,
    required this.measurementOptionId,
    required this.budgetOffset,
    required this.budgetOffsetType,
  });
}

class BudgetCalculationResult {
  final int originalBudget;
  final int tempBudget;
  final bool budgetHasChanged;

  BudgetCalculationResult({
    required this.originalBudget,
    required this.tempBudget,
    required this.budgetHasChanged,
  });

  @override
  String toString() {
    return 'BudgetCalculationResult(originalBudget: $originalBudget, tempBudget: $tempBudget, budgetHasChanged: $budgetHasChanged)';
  }
}

/// Extended FormUtils class with cascade deletion methods
/// These methods replicate the Java removeFormQuestionsFromRealm functionality
extension FormUtilsCascadeDeletion on FormUtils {
  /// Removes form questions and all their nested structures from Realm
  /// This method performs cascade deletion similar to the Java implementation
  /// Must be called within a realm.write() transaction
  static void removeFormQuestionsFromRealm(FormModel formModel) {
    try {
      logger(
          '📝 Removing form questions from realm for formId: ${formModel.formId}');

      // Process each question in the form
      for (final questionModel in formModel.questions) {
        // Clear QuestionModel child collections

        // Clear comment types
        questionModel.commentTypes.clear();

        // Clear question parts
        questionModel.questionParts.clear();

        // Clear measurements and their nested structures
        for (final measurementModel in questionModel.measurements) {
          // Clear measurement options
          measurementModel.measurementOptions.clear();

          // Clear measurement conditions
          measurementModel.measurementConditions.clear();

          // Clear measurement conditions multiple
          measurementModel.measurementConditionsMultiple.clear();

          // Clear measurement validations
          measurementModel.measurementValidations.clear();

          // Clear deprecated phototypes
          measurementModel.measurementPhototypesDeprecated.clear();
        }

        // Clear measurements collection
        questionModel.measurements.clear();

        // Clear question conditions
        questionModel.questionConditions.clear();

        // Clear photo tags
        questionModel.photoTagsTwo.clear();
        questionModel.photoTagsThree.clear();
      }

      // Clear form questions collection
      formModel.questions.clear();

      // Clear form question answers
      formModel.questionAnswers.clear();

      logger('✅ Successfully removed form questions from realm');
    } catch (e) {
      logger('❌ Error removing form questions from realm: $e');
    }
  }

  /// Safely removes a form from a task with cascade deletion
  /// This method handles the complete form removal process
  static void removeFormFromTask(TaskDetailModel task, FormModel formModel) {
    try {
      logger('📝 Removing form ${formModel.formId} from task ${task.taskId}');

      // First remove all nested structures
      removeFormQuestionsFromRealm(formModel);

      // Then remove the form from the task
      task.forms.remove(formModel);

      logger('✅ Successfully removed form from task');
    } catch (e) {
      logger('❌ Error removing form from task: $e');
    }
  }

  /// Updates individual form fields without replacing the entire form
  /// This is equivalent to the Java selective update approach
  static void updateFormFields(FormModel localForm, FormModel serverForm) {
    try {
      logger('📝 Updating form fields for formId: ${localForm.formId}');

      localForm.formName = serverForm.formName;
      localForm.briefUrl = serverForm.briefUrl;
      localForm.visionFormUrl = serverForm.visionFormUrl;
      localForm.isVisionForm = serverForm.isVisionForm;
      localForm.isMandatory = serverForm.isMandatory;
      localForm.modifiedTimeStampForm = serverForm.modifiedTimeStampForm;
      localForm.formAllowForward = serverForm.formAllowForward;
      localForm.formTypeId = serverForm.formTypeId;
      localForm.formCompleted = serverForm.formCompleted;
      localForm.formPreview = serverForm.formPreview;
      localForm.formShowPrice = serverForm.formShowPrice;
      localForm.minQty = serverForm.minQty;
      localForm.showQuestions = serverForm.showQuestions;
      localForm.kTotal = serverForm.kTotal;
      localForm.kCompleted = serverForm.kCompleted;
      localForm.dateStart = serverForm.dateStart;
      localForm.formInstanceId = serverForm.formInstanceId;

      logger('✅ Successfully updated form fields');
    } catch (e) {
      logger('❌ Error updating form fields: $e');
    }
  }

  /// Checks if a local form exists in server forms list
  /// Used for safe form deletion logic
  static bool formExistsInServerList(
      FormModel localForm, List<FormModel> serverForms) {
    try {
      return serverForms
          .any((serverForm) => serverForm.formId == localForm.formId);
    } catch (e) {
      logger('❌ Error checking form existence in server list: $e');
      return false;
    }
  }
}
