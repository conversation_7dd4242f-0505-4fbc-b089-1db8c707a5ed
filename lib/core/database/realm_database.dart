import 'package:realm/realm.dart';
import 'package:storetrack_app/features/home/<USER>/models/calendar_info_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/pos_response_item_model.dart';
import 'package:storetrack_app/features/home/<USER>/models/profile_model.dart';

import '../../features/home/<USER>/models/task_detail_model.dart';

class RealmDatabase {
  static RealmDatabase? _instance;
  late Realm _realm;

  RealmDatabase._() {
    final config = Configuration.local(
      [
        // TaskDetail related
        TaskDetailModel.schema,
        PhotoFolderModel.schema,
        SignatureFolderModel.schema,
        FormModel.schema,
        QuestionAnswerModel.schema,
        PosItemModel.schema,
        DocumentModel.schema,
        TaskalertModel.schema,
        TaskmemberModel.schema,
        FollowupTaskModel.schema,
        StocktakeModel.schema,
        PhotoModel.schema,
        SignatureModel.schema,
        QuestionModel.schema,
        QuestionPartModel.schema,
        MeasurementModel.schema,
        MeasurementOptionModel.schema,
        MeasurementConditionModel.schema,
        MeasurementValidationModel.schema,
        MeasurementPhototypesDeprecatedModel.schema,
        QuestionConditionModel.schema,
        PhotoTagsTModel.schema,
        CommentTypeModel.schema,
        FileElementModel.schema,
        // ResumePauseItem related
        ResumePauseItemModel.schema,
        // CalendarInfo related
        CalendarInfoModel.schema,
        // POS Response related
        PosResponseItemModel.schema,
        // Profile related
        ProfileModel.schema,
      ],
      schemaVersion: 9,
    );
    _realm = Realm(config);
  }

  static RealmDatabase get instance {
    _instance ??= RealmDatabase._();
    return _instance!;
  }

  Realm get realm => _realm;

  void clearAllData() {
    _realm.write(() {
      _realm.deleteAll<TaskDetailModel>();
      _realm.deleteAll<PhotoFolderModel>();
      _realm.deleteAll<SignatureFolderModel>();
      _realm.deleteAll<FormModel>();
      _realm.deleteAll<QuestionAnswerModel>();
      _realm.deleteAll<PosItemModel>();
      _realm.deleteAll<DocumentModel>();
      _realm.deleteAll<TaskalertModel>();
      _realm.deleteAll<TaskmemberModel>();
      _realm.deleteAll<FollowupTaskModel>();
      _realm.deleteAll<StocktakeModel>();
      _realm.deleteAll<PhotoModel>();
      _realm.deleteAll<SignatureModel>();
      _realm.deleteAll<QuestionModel>();
      _realm.deleteAll<QuestionPartModel>();
      _realm.deleteAll<MeasurementModel>();
      _realm.deleteAll<MeasurementOptionModel>();
      _realm.deleteAll<MeasurementConditionModel>();
      _realm.deleteAll<MeasurementValidationModel>();
      _realm.deleteAll<PosResponseItemModel>();
      _realm.deleteAll<MeasurementPhototypesDeprecatedModel>();
      _realm.deleteAll<QuestionConditionModel>();
      _realm.deleteAll<PhotoTagsTModel>();
      _realm.deleteAll<CommentTypeModel>();
      _realm.deleteAll<FileElementModel>();
      _realm.deleteAll<ResumePauseItemModel>();
      _realm.deleteAll<CalendarInfoModel>();
      _realm.deleteAll<ProfileModel>();
    });
  }

  void close() {
    _realm.close();
  }
}
