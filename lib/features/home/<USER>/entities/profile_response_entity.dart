import 'package:equatable/equatable.dart';

class ProfileResponseEntity extends Equatable {
  final ProfileDataEntity? data;

  const ProfileResponseEntity({
    this.data,
  });

  @override
  List<Object?> get props => [data];
}

class ProfileDataEntity extends Equatable {
  final String? token;
  final int? userId;
  final int? contractorId;
  final int? countryId;
  final int? stateId;
  final String? firstName;
  final String? lastName;
  final String? address;
  final String? email;
  final String? country;
  final String? state;
  final String? suburb;
  final String? postcode;
  final String? pAddress;
  final String? pSuburb;
  final String? pPostcode;
  final int? pCountryId;
  final String? pCountry;
  final String? pRegion;
  final int? pRegionId;
  final String? pDeliveryComment;
  final String? mobile;
  final String? profileImageUrl;
  final String? modifiedTimeStampProfile;
  final bool? adminAccess;
  final bool? createTask;
  final String? orgIDs;

  const ProfileDataEntity({
    this.token,
    this.userId,
    this.contractorId,
    this.countryId,
    this.stateId,
    this.firstName,
    this.lastName,
    this.address,
    this.email,
    this.country,
    this.state,
    this.suburb,
    this.postcode,
    this.pAddress,
    this.pSuburb,
    this.pPostcode,
    this.pCountryId,
    this.pCountry,
    this.pRegion,
    this.pRegionId,
    this.pDeliveryComment,
    this.mobile,
    this.profileImageUrl,
    this.modifiedTimeStampProfile,
    this.adminAccess,
    this.createTask,
    this.orgIDs,
  });

  @override
  List<Object?> get props => [
        token,
        userId,
        contractorId,
        countryId,
        stateId,
        firstName,
        lastName,
        address,
        email,
        country,
        state,
        suburb,
        postcode,
        pAddress,
        pSuburb,
        pPostcode,
        pCountryId,
        pCountry,
        pRegion,
        pRegionId,
        pDeliveryComment,
        mobile,
        profileImageUrl,
        modifiedTimeStampProfile,
        adminAccess,
        createTask,
        orgIDs,
      ];
}
